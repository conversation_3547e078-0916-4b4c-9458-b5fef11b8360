# Audio Couch - AI Screen Sharing & Conversation App

An interactive desktop application that enables real-time screen sharing and voice conversations with Google's Gemini AI.

## Features

- 🎤 Real-time voice conversation with AI
- 🖥️ Screen sharing capability
- 📹 Camera support
- 💬 Live transcription
- 🎨 Modern React UI with Tailwind CSS

## Prerequisites

- Python 3.8+
- Node.js 16+
- Google Gemini API key
- macOS (for screen recording permissions)

## Setup Instructions

### 1. Clone the repository

```bash
cd "/Users/<USER>/Desktop/Audio couch"
```

### 2. Set up the backend

```bash
# Install Python dependencies
pip install -r requirements.txt

# Create .env file and add your Gemini API key
echo "GEMINI_API_KEY=your_actual_key_here" > .env
```

### 3. Set up the frontend

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Return to root directory
cd ..
```

### 4. Run the application

Open two terminal windows:

**Terminal 1 - Backend:**
```bash
uvicorn main:app --host 127.0.0.1 --reload
```

**Terminal 2 - Frontend:**
```bash
cd frontend
npm run dev
```

### 5. Access the application

Open your browser and navigate to `http://localhost:5173`

## Usage

1. Click the microphone button to start a session
2. Choose between Screen, Camera, or Audio-only mode
3. Grant necessary permissions when prompted
4. Start talking to the AI!
5. Use the text input to send messages
6. Click the stop button to end the session

## Troubleshooting

### macOS Permissions

You may need to grant the following permissions:
- Microphone access
- Screen recording (System Preferences > Security & Privacy > Screen Recording)
- Camera access (if using camera mode)

### Audio Issues

If you experience audio issues:
1. Check your default input/output devices
2. Ensure PyAudio is properly installed: `pip install --upgrade pyaudio`
3. On macOS, you might need: `brew install portaudio`

### API Key Issues

Make sure your `.env` file contains a valid Gemini API key:
```
GEMINI_API_KEY=your_actual_key_here
```

## Architecture

- **Backend**: FastAPI with WebSocket support
- **Frontend**: React + Vite + Tailwind CSS
- **AI**: Google Gemini 2.5 Flash with native audio
- **Audio**: PyAudio for capture/playback
- **Screen Capture**: MSS library

## License

This project is for demonstration purposes.