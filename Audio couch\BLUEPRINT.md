# Audio Couch - AI Screen Sharing & Conversation App Blueprint

## 🎯 Core Concept
A local desktop application that enables real-time screen sharing and voice conversations with Google's Gemini AI using their native audio capabilities. The app captures screen content, processes audio input/output, and provides a web-based UI for control.

## 🏗️ Architecture Overview

```
┌─────────────────────┐     ┌─────────────────────┐
│   React Frontend    │────▶│  FastAPI Backend    │
│  (localhost:5173)   │◀────│  (localhost:8000)   │
└─────────────────────┘     └──────────┬──────────┘
                                       │
                            ┌──────────▼──────────┐
                            │  Gemini AI API     │
                            │ (Audio + Vision)   │
                            └────────────────────┘
```

## 📋 Key Features
1. **Screen Capture**: Real-time screen sharing with Gemini AI
2. **Audio Conversation**: Two-way voice communication
3. **Mode Selection**: Toggle between camera/screen/audio-only modes
4. **Live Transcription**: Display AI responses and conversation history
5. **Session Management**: Start/stop sessions with clean state handling

## 🔄 Data Flow Design

```
1. User starts session → Frontend → Backend creates Gemini session
2. Audio capture → PyAudio → WebSocket → Gemini API
3. Screen capture → MSS → Base64 encoding → Gemini API  
4. Gemini response → Audio stream → PyAudio output
5. Transcription → WebSocket → Frontend display
```

## 💻 Technical Components

### Backend (main.py)
- **FastAPI server** with WebSocket support
- **Audio processing**: PyAudio for mic input/speaker output
- **Screen capture**: MSS library for cross-platform screenshots
- **Gemini client**: Async connection management
- **Session handling**: Concurrent audio/video/text streams

### Frontend (React + Vite)
- **Main UI**: Session controls, mode selection
- **Transcript view**: Real-time conversation display
- **Status indicators**: Connection state, active mode
- **Settings panel**: Audio device selection, quality options

## 📁 File Structure

```
audio-couch/
├── main.py                 # FastAPI backend
├── requirements.txt        # Python dependencies
├── .env                   # API keys
├── frontend/              # React app
│   ├── package.json
│   ├── vite.config.js
│   ├── tailwind.config.js
│   ├── index.html
│   └── src/
│       ├── main.jsx
│       ├── App.jsx
│       ├── components/
│       │   ├── SessionControl.jsx
│       │   ├── TranscriptView.jsx
│       │   ├── ModeSelector.jsx
│       │   └── StatusBar.jsx
│       └── lib/
│           └── api.js     # WebSocket client
```

## 🔌 API Endpoints

```python
POST   /api/session/start    # Start Gemini session
DELETE /api/session/stop     # End current session
GET    /api/session/status   # Get session info
WS     /ws/audio            # Audio stream WebSocket
WS     /ws/transcript       # Text updates WebSocket
GET    /api/devices         # List audio devices
```

## 🔧 Implementation Details

### Backend Key Classes
```python
class GeminiSession:
    - manage connection lifecycle
    - handle audio/video streams
    - process AI responses

class AudioProcessor:
    - capture microphone input
    - play AI audio output
    - handle device selection

class ScreenCapture:
    - grab screenshots at intervals
    - encode for transmission
    - optimize image quality/size
```

### Frontend Components
- **SessionControl**: Start/stop button with loading states
- **ModeSelector**: Radio buttons for camera/screen/audio modes
- **TranscriptView**: Scrollable chat-like interface
- **StatusBar**: Connection indicator, current mode, duration

## 🚀 Critical Success Factors

1. **Async Architecture**: All I/O operations must be non-blocking
2. **Queue Management**: Separate queues for audio in/out to prevent overflow
3. **Error Handling**: Graceful degradation if camera/mic unavailable
4. **Resource Cleanup**: Properly close streams on session end
5. **CORS Configuration**: Frontend-backend communication setup

## 📦 Dependencies

**Python (requirements.txt)**:
```
fastapi
uvicorn
websockets
python-multipart
google-generativeai
opencv-python
pyaudio
pillow
mss
python-dotenv
termcolor
```

**Frontend (package.json)**:
```
react
react-dom
@vitejs/plugin-react
vite
tailwindcss
@radix-ui/react-*
lucide-react
clsx
tailwind-merge
```

## 🎯 Setup & Run Instructions

1. **Environment Setup**
   ```bash
   echo "GEMINI_API_KEY=your_key_here" > .env
   pip install -r requirements.txt
   ```

2. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

3. **Start Backend**
   ```bash
   uvicorn main:app --host 127.0.0.1 --reload
   ```

## ⚠️ Known Challenges & Solutions

1. **Audio Latency**: Use small chunk sizes, optimize buffer management
2. **Screen Capture Performance**: Throttle to 1 FPS, compress images
3. **WebSocket Stability**: Implement reconnection logic, heartbeat checks
4. **Cross-platform Audio**: Test PyAudio device enumeration on macOS
5. **Memory Management**: Clear queues on session end, limit history

## 🎯 Confidence Assessment: 95%

This blueprint provides a solid foundation for building the Audio Couch app. The architecture leverages proven technologies (FastAPI, React, WebSockets) and follows the provided technical requirements. The main risks are around audio/video synchronization and cross-platform compatibility, but these are manageable with the proposed solutions.