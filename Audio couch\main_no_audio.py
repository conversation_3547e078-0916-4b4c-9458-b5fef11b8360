import os
import asyncio
import base64
import io
import json
from typing import Optional, Dict, Any
from datetime import datetime
import traceback

from fastapi import Fast<PERSON><PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import cv2
import PIL.Image
import mss
from termcolor import colored
from dotenv import load_dotenv

from google import genai
from google.genai import types

load_dotenv()

# Constants (audio disabled)
MODEL = "models/gemini-2.5-flash-exp-native-audio-thinking-dialog"
DEFAULT_MODE = "screen"

# Initialize FastAPI
app = FastAPI(title="Audio Couch API (No Audio)")

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize Gemini client
client = genai.Client(
    http_options={"api_version": "v1beta"},
    api_key=os.environ.get("GEMINI_API_KEY"),
)

CONFIG = types.LiveConnectConfig(
    response_modalities=["TEXT"],  # Only text, no audio
    media_resolution="MEDIA_RESOLUTION_MEDIUM",
    context_window_compression=types.ContextWindowCompressionConfig(
        trigger_tokens=25600,
        sliding_window=types.SlidingWindow(target_tokens=12800),
    ),
)

# Global session manager
class SessionManager:
    def __init__(self):
        self.active_session: Optional[GeminiSession] = None
        self.status = "idle"
        self.mode = DEFAULT_MODE
        self.start_time: Optional[datetime] = None
        
    async def start_session(self, mode: str = DEFAULT_MODE):
        if self.active_session:
            await self.stop_session()
            
        self.mode = mode
        self.status = "starting"
        self.start_time = datetime.now()
        
        print(colored(f"Starting session in {mode} mode (no audio)...", "green"))
        
        self.active_session = GeminiSession(mode)
        await self.active_session.start()
        
        self.status = "active"
        return {"status": "active", "mode": mode, "start_time": self.start_time.isoformat()}
        
    async def stop_session(self):
        if self.active_session:
            print(colored("Stopping session...", "yellow"))
            await self.active_session.stop()
            self.active_session = None
            
        self.status = "idle"
        self.start_time = None
        return {"status": "stopped"}
        
    def get_status(self):
        return {
            "status": self.status,
            "mode": self.mode if self.status == "active" else None,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "duration": (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
        }

session_manager = SessionManager()

# WebSocket connections manager
class ConnectionManager:
    def __init__(self):
        self.transcript_connections: list[WebSocket] = []
        
    async def connect_transcript(self, websocket: WebSocket):
        await websocket.accept()
        self.transcript_connections.append(websocket)
        
    def disconnect_transcript(self, websocket: WebSocket):
        self.transcript_connections.remove(websocket)
        
    async def broadcast_transcript(self, message: dict):
        disconnected = []
        for connection in self.transcript_connections:
            try:
                await connection.send_json(message)
            except:
                disconnected.append(connection)
                
        for conn in disconnected:
            self.transcript_connections.remove(conn)

manager = ConnectionManager()

# Gemini session handler (no audio)
class GeminiSession:
    def __init__(self, mode: str = DEFAULT_MODE):
        self.mode = mode
        self.session = None
        self.out_queue = None
        self.tasks = []
        self.running = False
        
    async def start(self):
        self.running = True
        self.out_queue = asyncio.Queue(maxsize=5)
        
        try:
            self.session = await client.aio.live.connect(model=MODEL, config=CONFIG)
            
            # Start async tasks (no audio tasks)
            self.tasks = [
                asyncio.create_task(self.send_realtime()),
                asyncio.create_task(self.receive_text()),
            ]
            
            if self.mode == "screen":
                self.tasks.append(asyncio.create_task(self.capture_screen()))
            elif self.mode == "camera":
                self.tasks.append(asyncio.create_task(self.capture_camera()))
                
            # Send initial message
            await self.session.send(
                input="Hello! I can see your screen. How can I help you today? (Note: Audio is disabled in this version)",
                end_of_turn=True
            )
            
        except Exception as e:
            print(colored(f"Error starting session: {e}", "red"))
            traceback.print_exc()
            await self.stop()
            raise
            
    async def stop(self):
        self.running = False
        
        # Cancel all tasks
        for task in self.tasks:
            task.cancel()
            
        # Wait for tasks to complete
        await asyncio.gather(*self.tasks, return_exceptions=True)
        
        # Close session
        if self.session:
            await self.session.close()
            
    async def send_text(self, text: str):
        if self.session:
            await self.session.send(input=text, end_of_turn=True)
            
    async def send_realtime(self):
        while self.running:
            try:
                msg = await asyncio.wait_for(self.out_queue.get(), timeout=0.1)
                await self.session.send(input=msg)
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                print(colored(f"Error in send_realtime: {e}", "red"))
                
    async def receive_text(self):
        while self.running:
            try:
                turn = self.session.receive()
                async for response in turn:
                    if text := response.text:
                        print(colored(f"AI: {text}", "cyan"))
                        await manager.broadcast_transcript({
                            "type": "ai",
                            "text": text,
                            "timestamp": datetime.now().isoformat()
                        })
                        
            except Exception as e:
                if self.running:
                    print(colored(f"Error in receive_text: {e}", "red"))
                    
    def _capture_screen(self):
        sct = mss.mss()
        monitor = sct.monitors[0]
        screenshot = sct.grab(monitor)
        
        img = PIL.Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
        img.thumbnail([1024, 1024])
        
        image_io = io.BytesIO()
        img.save(image_io, format="jpeg", quality=85)
        image_io.seek(0)
        
        return {
            "mime_type": "image/jpeg",
            "data": base64.b64encode(image_io.read()).decode()
        }
        
    async def capture_screen(self):
        while self.running:
            try:
                frame = await asyncio.to_thread(self._capture_screen)
                await self.out_queue.put(frame)
                await asyncio.sleep(1.0)  # 1 FPS
            except Exception as e:
                print(colored(f"Error in capture_screen: {e}", "red"))
                
    def _capture_camera(self, cap):
        ret, frame = cap.read()
        if not ret:
            return None
            
        # Convert BGR to RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        img = PIL.Image.fromarray(frame_rgb)
        img.thumbnail([1024, 1024])
        
        image_io = io.BytesIO()
        img.save(image_io, format="jpeg", quality=85)
        image_io.seek(0)
        
        return {
            "mime_type": "image/jpeg",
            "data": base64.b64encode(image_io.read()).decode()
        }
        
    async def capture_camera(self):
        cap = await asyncio.to_thread(cv2.VideoCapture, 0)
        
        while self.running:
            try:
                frame = await asyncio.to_thread(self._capture_camera, cap)
                if frame:
                    await self.out_queue.put(frame)
                await asyncio.sleep(1.0)  # 1 FPS
            except Exception as e:
                print(colored(f"Error in capture_camera: {e}", "red"))
                
        cap.release()

# API Endpoints
@app.get("/")
async def root():
    return {"message": "Audio Couch API (No Audio)", "version": "1.0.0"}

@app.post("/api/session/start")
async def start_session(mode: str = DEFAULT_MODE):
    try:
        if mode not in ["camera", "screen", "none"]:
            raise HTTPException(status_code=400, detail="Invalid mode")
            
        result = await session_manager.start_session(mode)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/session/stop")
async def stop_session():
    try:
        result = await session_manager.stop_session()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/session/status")
async def get_session_status():
    return session_manager.get_status()

@app.get("/api/devices")
async def get_audio_devices():
    # Return empty devices list since audio is disabled
    return {"devices": [], "note": "Audio disabled in this version"}

@app.websocket("/ws/transcript")
async def websocket_transcript(websocket: WebSocket):
    await manager.connect_transcript(websocket)
    try:
        while True:
            # Keep connection alive
            await websocket.receive_text()
    except WebSocketDisconnect:
        manager.disconnect_transcript(websocket)

@app.post("/api/message")
async def send_message(message: dict):
    if not session_manager.active_session:
        raise HTTPException(status_code=400, detail="No active session")
        
    text = message.get("text", "")
    if text:
        await manager.broadcast_transcript({
            "type": "user",
            "text": text,
            "timestamp": datetime.now().isoformat()
        })
        await session_manager.active_session.send_text(text)
        
    return {"status": "sent"}

if __name__ == "__main__":
    print(colored("Starting Audio Couch API (No Audio)...", "green"))
    print(colored(f"Using model: {MODEL}", "blue"))
    print(colored("Note: Audio features are disabled in this version", "yellow"))
    
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000, reload=True)
