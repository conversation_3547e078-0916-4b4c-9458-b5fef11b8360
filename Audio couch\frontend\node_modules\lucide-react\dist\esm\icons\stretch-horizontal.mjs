/**
 * lucide-react v0.0.1 - ISC
 */

import createLucideIcon from '../createLucideIcon.mjs';

const StretchHorizontal = createLucideIcon("StretchHorizontal", [
  [
    "rect",
    { width: "20", height: "6", x: "2", y: "4", rx: "2", key: "qdearl" }
  ],
  [
    "rect",
    { width: "20", height: "6", x: "2", y: "14", rx: "2", key: "1xrn6j" }
  ]
]);

export { StretchHorizontal as default };
//# sourceMappingURL=stretch-horizontal.mjs.map
